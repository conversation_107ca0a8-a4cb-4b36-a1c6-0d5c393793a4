import { NextRequest, NextResponse } from 'next/server';
import { CompanyService } from '../../services/company/CompanyService';
import { UserService } from '../../services/user/UserService';

const companyService = new CompanyService();
const userService = new UserService();

/**
 * GET /api/backbone/oneid/admin/statistics
 * Get comprehensive portal statistics
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d';
    const includeDetails = searchParams.get('includeDetails') === 'true';

    // Get company statistics
    const hierarchyStats = await companyService.getHierarchyStats();
    const allCompanies = await companyService.getAllCompanies();
    const companiesWithEmployeeCount = await companyService.getCompaniesWithEmployeeCount();

    // Get user statistics
    const allUsers = await userService.getAllUsers();
    const activeUsers = allUsers.filter(user => user.status === 'active');

    // Calculate user type distribution
    const userTypeDistribution = allUsers.reduce((acc, user) => {
      acc[user.userType] = (acc[user.userType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate recent activity (mock data for now)
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const recentCompanies = allCompanies.filter(company => 
      new Date(company.createdAt) > thirtyDaysAgo
    ).length;

    const recentUsers = allUsers.filter(user => 
      new Date(user.createdAt) > thirtyDaysAgo
    ).length;

    // Mock service and consumption data
    const serviceStats = {
      totalServices: 8,
      activeSubscriptions: Math.floor(allCompanies.length * 0.8),
      totalRevenue: 125000,
      averageUsage: 78.5
    };

    const consumptionStats = {
      apiCalls: 1250000,
      storageUsed: 2.4,
      bandwidthUsed: 156.7,
      activeConnections: 342
    };

    // Calculate growth rates (mock data)
    const growthStats = {
      companiesGrowth: 15.2,
      usersGrowth: 23.8,
      revenueGrowth: 18.5,
      usageGrowth: 12.3
    };

    const statistics = {
      companies: {
        total: hierarchyStats.totalCompanies,
        active: hierarchyStats.activeCompanies,
        inactive: hierarchyStats.inactiveCompanies,
        rootCompanies: hierarchyStats.rootCompanies,
        maxHierarchyDepth: hierarchyStats.maxDepth,
        recentlyCreated: recentCompanies,
        byLevel: hierarchyStats.companiesByLevel
      },
      users: {
        total: allUsers.length,
        active: activeUsers.length,
        byType: {
          individual: userTypeDistribution.individual || 0,
          employee: userTypeDistribution.employee || 0,
          company_admin: userTypeDistribution.company_admin || 0
        },
        recentlyRegistered: recentUsers,
        activeToday: Math.floor(activeUsers.length * 0.65)
      },
      services: serviceStats,
      consumption: consumptionStats,
      growth: growthStats,
      timestamp: new Date().toISOString()
    };

    // Add detailed information if requested
    if (includeDetails) {
      const detailedStats = {
        ...statistics,
        details: {
          topCompanies: companiesWithEmployeeCount
            .sort((a, b) => b.employeeCount - a.employeeCount)
            .slice(0, 10)
            .map(company => ({
              id: company.id,
              name: company.name,
              employeeCount: company.employeeCount,
              status: company.status,
              createdAt: company.createdAt
            })),
          recentActivity: [
            ...allCompanies
              .filter(company => new Date(company.createdAt) > thirtyDaysAgo)
              .map(company => ({
                type: 'company_created',
                entityId: company.id,
                entityName: company.name,
                timestamp: company.createdAt,
                details: { parentId: company.parentCompanyId }
              })),
            ...allUsers
              .filter(user => new Date(user.createdAt) > thirtyDaysAgo)
              .map(user => ({
                type: 'user_registered',
                entityId: user.id,
                entityName: `${user.firstName} ${user.lastName}`,
                timestamp: user.createdAt,
                details: { userType: user.userType }
              }))
          ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 20)
        }
      };

      return NextResponse.json({
        success: true,
        data: detailedStats
      });
    }

    return NextResponse.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('Error fetching portal statistics:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}

/**
 * GET /api/backbone/oneid/admin/statistics/trends
 * Get trend data for charts and analytics
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { metric, timeRange, granularity } = body;

    // Mock trend data generation
    const generateTrendData = (metric: string, days: number) => {
      const data = [];
      const now = new Date();
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const baseValue = Math.floor(Math.random() * 100) + 50;
        
        data.push({
          date: date.toISOString().split('T')[0],
          value: baseValue,
          metric: metric
        });
      }
      
      return data;
    };

    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const trendData = generateTrendData(metric, days);

    return NextResponse.json({
      success: true,
      data: {
        metric,
        timeRange,
        granularity,
        data: trendData
      }
    });

  } catch (error) {
    console.error('Error fetching trend data:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}
