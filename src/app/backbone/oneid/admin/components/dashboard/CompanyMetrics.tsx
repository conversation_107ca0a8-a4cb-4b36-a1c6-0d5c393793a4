'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Activity,
  Users,
  TrendingUp,
  Eye,
  Plus,
  ArrowUpRight
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TreeMap,
  Cell
} from 'recharts';
import Link from 'next/link';

interface CompanyMetricsProps {
  stats: any;
}

export default function CompanyMetrics({ stats }: CompanyMetricsProps) {
  if (!stats) return null;

  // Mock data for company hierarchy visualization
  const hierarchyData = [
    { name: 'TechCorp Solutions', size: 150, children: 2, level: 0 },
    { name: 'TechCorp AI', size: 25, children: 0, level: 1 },
    { name: 'TechCorp Cloud', size: 35, children: 0, level: 1 },
    { name: 'HealthPlus Medical', size: 75, children: 1, level: 0 },
    { name: 'HealthPlus Cardio', size: 15, children: 0, level: 1 },
    { name: 'RetailMax Corp', size: 200, children: 2, level: 0 },
    { name: 'RetailMax Online', size: 45, children: 0, level: 1 },
    { name: 'RetailMax Stores', size: 120, children: 0, level: 1 },
    { name: 'Green Finance', size: 45, children: 0, level: 0 },
    { name: 'EduLearn Academy', size: 25, children: 0, level: 0 },
  ];

  const companyStatusData = [
    { status: 'Active', count: stats.active, color: '#10B981' },
    { status: 'Inactive', count: stats.inactive, color: '#EF4444' },
  ];

  const companyGrowthData = [
    { month: 'Jan', companies: 5, employees: 180 },
    { month: 'Feb', companies: 6, employees: 195 },
    { month: 'Mar', companies: 7, employees: 220 },
    { month: 'Apr', companies: 8, employees: 245 },
    { month: 'May', companies: 9, employees: 270 },
    { month: 'Jun', companies: 10, employees: 295 },
  ];

  const topCompanies = [
    { name: 'RetailMax Corporation', employees: 200, revenue: 45000, growth: 15.2 },
    { name: 'TechCorp Solutions', employees: 150, revenue: 38000, growth: 12.8 },
    { name: 'HealthPlus Medical Group', employees: 75, revenue: 22000, growth: 8.5 },
    { name: 'Green Finance Solutions', employees: 45, revenue: 15000, growth: 6.2 },
    { name: 'EduLearn Academy', employees: 25, revenue: 8000, growth: 4.1 },
  ];

  return (
    <div className="space-y-6">
      {/* Company Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.active} active, {stats.inactive} inactive
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Root Companies</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rootCompanies}</div>
            <p className="text-xs text-muted-foreground">
              Independent organizations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hierarchy Depth</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.maxHierarchyDepth}</div>
            <p className="text-xs text-muted-foreground">
              Maximum levels deep
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recently Created</CardTitle>
            <Plus className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentlyCreated}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Company Growth */}
        <Card>
          <CardHeader>
            <CardTitle>Company Growth</CardTitle>
            <CardDescription>
              Number of companies and total employees over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={companyGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="companies" fill="#3B82F6" name="Companies" />
                <Bar dataKey="employees" fill="#10B981" name="Total Employees" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Company Hierarchy Visualization */}
        <Card>
          <CardHeader>
            <CardTitle>Company Hierarchy</CardTitle>
            <CardDescription>
              Visual representation of company structure and employee distribution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <TreeMap
                data={hierarchyData}
                dataKey="size"
                aspectRatio={4/3}
                stroke="#fff"
                fill="#3B82F6"
              >
                {hierarchyData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.level === 0 ? '#3B82F6' : '#93C5FD'} 
                  />
                ))}
              </TreeMap>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Companies Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Top Companies</CardTitle>
            <CardDescription>
              Companies ranked by employee count and revenue
            </CardDescription>
          </div>
          <Link href="/backbone/oneid/company-portal/companies">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topCompanies.map((company, index) => (
              <div key={company.name} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                    <span className="text-sm font-semibold text-blue-600">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold">{company.name}</h4>
                    <p className="text-sm text-gray-600">
                      {company.employees} employees
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">
                    ${company.revenue.toLocaleString()}
                  </div>
                  <div className="flex items-center text-sm text-green-600">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    {company.growth}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Company Management Actions</CardTitle>
          <CardDescription>
            Quick actions for managing companies and their relationships
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/backbone/oneid/admin/onboarding">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Plus className="h-5 w-5 mb-1" />
                Create New Company
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/companies/hierarchy">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Activity className="h-5 w-5 mb-1" />
                View Hierarchy
              </Button>
            </Link>
            <Link href="/backbone/oneid/admin/companies">
              <Button variant="outline" className="w-full h-16 flex flex-col">
                <Building2 className="h-5 w-5 mb-1" />
                Manage Companies
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
